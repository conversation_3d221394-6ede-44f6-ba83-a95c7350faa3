{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-01-12 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "patient_name", "patient_id", "assessment_date", "assessed_by", "column_break_5", "status", "priority", "section_break_demographics", "age", "gender", "column_break_demographics", "living_situation", "social_support", "section_break_cognitive", "mmse_score", "moca_score", "column_break_cognitive", "cognitive_notes", "section_break_functional", "adl_score", "iadl_score", "functional_dependency", "column_break_functional", "mobility", "fall_risk", "section_break_health", "primary_diagnosis", "comorbidities", "gds_score", "column_break_health", "care_complexity_index", "section_break_prediction", "prediction_status", "predicted_care_type", "prediction_confidence", "column_break_prediction", "prediction_date", "model_version", "section_break_notes", "clinical_notes", "recommendations"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "PA-.YYYY.-", "reqd": 1}, {"fieldname": "patient_name", "fieldtype": "Data", "label": "Patient Name", "reqd": 1}, {"fieldname": "patient_id", "fieldtype": "Link", "label": "Patient ID", "options": "Patient"}, {"fieldname": "assessment_date", "fieldtype": "Date", "label": "Assessment Date", "reqd": 1, "default": "Today"}, {"fieldname": "assessed_by", "fieldtype": "Link", "label": "Assessed By", "options": "User", "reqd": 1, "default": "__user"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Draft\nCompleted\nReviewed\nApproved", "default": "Draft"}, {"fieldname": "priority", "fieldtype": "Select", "label": "Priority", "options": "Low\nMedium\nHigh\nUrgent", "default": "Medium"}, {"fieldname": "section_break_demographics", "fieldtype": "Section Break", "label": "Demographics"}, {"fieldname": "age", "fieldtype": "Int", "label": "Age", "reqd": 1}, {"fieldname": "gender", "fieldtype": "Select", "label": "Gender", "options": "Male\nFemale", "reqd": 1}, {"fieldname": "column_break_demographics", "fieldtype": "Column Break"}, {"fieldname": "living_situation", "fieldtype": "Select", "label": "Living Situation", "options": "Alone\nWith_Family\nWith_Spouse", "reqd": 1}, {"fieldname": "social_support", "fieldtype": "Select", "label": "Social Support Level", "options": "Low\nMedium\nHigh", "reqd": 1}, {"fieldname": "section_break_cognitive", "fieldtype": "Section Break", "label": "Cognitive Assessment"}, {"fieldname": "mmse_score", "fieldtype": "Int", "label": "MMSE Score (0-30)", "reqd": 1}, {"fieldname": "moca_score", "fieldtype": "Int", "label": "MoCA Score (0-30)", "reqd": 1}, {"fieldname": "column_break_cognitive", "fieldtype": "Column Break"}, {"fieldname": "cognitive_notes", "fieldtype": "Text", "label": "Cognitive Assessment Notes"}, {"fieldname": "section_break_functional", "fieldtype": "Section Break", "label": "Functional Assessment"}, {"fieldname": "adl_score", "fieldtype": "Int", "label": "ADL Score (0-6)", "reqd": 1}, {"fieldname": "iadl_score", "fieldtype": "Int", "label": "IADL Score (0-8)", "reqd": 1}, {"fieldname": "functional_dependency", "fieldtype": "Int", "label": "Functional Dependency Score", "read_only": 1}, {"fieldname": "column_break_functional", "fieldtype": "Column Break"}, {"fieldname": "mobility", "fieldtype": "Select", "label": "Mobility", "options": "Independent\nWalker\nWheelchair\nBedbound", "reqd": 1}, {"fieldname": "fall_risk", "fieldtype": "Select", "label": "Fall Risk", "options": "Low\nMedium\nHigh", "reqd": 1}, {"fieldname": "section_break_health", "fieldtype": "Section Break", "label": "Health Information"}, {"fieldname": "primary_diagnosis", "fieldtype": "Select", "label": "Primary Diagnosis", "options": "Dementia\nStroke\nHeart_Disease\nDiabetes\nCOPD\nArthritis\nOther", "reqd": 1}, {"fieldname": "comorbidities", "fieldtype": "Int", "label": "Number of Comorbidities", "reqd": 1}, {"fieldname": "gds_score", "fieldtype": "Int", "label": "GDS Score (0-15)", "reqd": 1}, {"fieldname": "column_break_health", "fieldtype": "Column Break"}, {"fieldname": "care_complexity_index", "fieldtype": "Int", "label": "Care Complexity Index", "read_only": 1}, {"fieldname": "section_break_prediction", "fieldtype": "Section Break", "label": "ML Prediction Results"}, {"fieldname": "prediction_status", "fieldtype": "Select", "label": "Prediction Status", "options": "Pending\nCompleted\nError", "read_only": 1}, {"fieldname": "predicted_care_type", "fieldtype": "Select", "label": "Predicted Care Type", "options": "Independent_Living\nHome_Care\nAssisted_Living\nMemory_Care", "read_only": 1}, {"fieldname": "prediction_confidence", "fieldtype": "Percent", "label": "Prediction Confidence", "read_only": 1}, {"fieldname": "column_break_prediction", "fieldtype": "Column Break"}, {"fieldname": "prediction_date", "fieldtype": "Datetime", "label": "Prediction Date", "read_only": 1}, {"fieldname": "model_version", "fieldtype": "Data", "label": "Model Version", "read_only": 1}, {"fieldname": "section_break_notes", "fieldtype": "Section Break", "label": "Clinical Notes"}, {"fieldname": "clinical_notes", "fieldtype": "Text Editor", "label": "Clinical Notes"}, {"fieldname": "recommendations", "fieldtype": "Text Editor", "label": "Care Recommendations"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-01-12 10:00:00.000000", "modified_by": "Administrator", "module": "Care Placement", "name": "Patient Assessment", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Healthcare Practitioner", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Healthcare Administrator", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "patient_name", "track_changes": 1}