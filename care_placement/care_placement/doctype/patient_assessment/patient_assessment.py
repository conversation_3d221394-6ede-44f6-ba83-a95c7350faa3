# -*- coding: utf-8 -*-
"""
Patient Assessment DocType Controller
Handles business logic for patient assessments and ML predictions
"""

import frappe
from frappe.model.document import Document
from frappe.utils import flt, cint
from care_placement.ml_predictor import get_predictor

class PatientAssessment(Document):
    """Patient Assessment Document Controller"""
    
    def validate(self):
        """Validate patient assessment data"""
        self.validate_scores()
        self.calculate_derived_scores()
        self.set_priority()
    
    def validate_scores(self):
        """Validate assessment scores are within valid ranges"""
        # MMSE Score validation
        if self.mmse_score < 0 or self.mmse_score > 30:
            frappe.throw("MMSE Score must be between 0 and 30")
        
        # MoCA Score validation
        if self.moca_score < 0 or self.moca_score > 30:
            frappe.throw("MoCA Score must be between 0 and 30")
        
        # ADL Score validation
        if self.adl_score < 0 or self.adl_score > 6:
            frappe.throw("ADL Score must be between 0 and 6")
        
        # IADL Score validation
        if self.iadl_score < 0 or self.iadl_score > 8:
            frappe.throw("IADL Score must be between 0 and 8")
        
        # GDS Score validation
        if self.gds_score < 0 or self.gds_score > 15:
            frappe.throw("GDS Score must be between 0 and 15")
        
        # Age validation
        if self.age < 18 or self.age > 120:
            frappe.throw("Age must be between 18 and 120")
        
        # Comorbidities validation
        if self.comorbidities < 0:
            frappe.throw("Number of comorbidities cannot be negative")
    
    def calculate_derived_scores(self):
        """Calculate derived scores automatically"""
        # Functional Dependency Score
        self.functional_dependency = (6 - self.adl_score) + (8 - self.iadl_score)
        
        # Care Complexity Index
        complexity_score = 0
        
        if self.mmse_score < 20:
            complexity_score += 2
        if self.adl_score < 4:
            complexity_score += 2
        if self.fall_risk == 'High':
            complexity_score += 2
        if self.comorbidities > 3:
            complexity_score += 2
        if self.social_support == 'Low':
            complexity_score += 1
        
        self.care_complexity_index = complexity_score
    
    def set_priority(self):
        """Set assessment priority based on risk factors"""
        if not self.priority or self.priority == "Medium":
            # High priority conditions
            if (self.mmse_score < 15 or 
                self.fall_risk == 'High' or 
                self.care_complexity_index >= 8 or
                self.functional_dependency >= 10):
                self.priority = "High"
            
            # Urgent priority conditions
            elif (self.mmse_score < 10 or 
                  self.adl_score <= 2 or
                  self.care_complexity_index >= 10):
                self.priority = "Urgent"
            
            # Low priority conditions
            elif (self.mmse_score >= 25 and 
                  self.adl_score >= 5 and 
                  self.fall_risk == 'Low'):
                self.priority = "Low"
    
    def after_insert(self):
        """Actions after document creation"""
        # Auto-trigger prediction if all required fields are filled
        if self.is_complete_assessment():
            self.trigger_ml_prediction()
    
    def on_update(self):
        """Actions after document update"""
        # Re-trigger prediction if assessment data changed
        if self.has_value_changed(['mmse_score', 'moca_score', 'adl_score', 'iadl_score', 
                                  'mobility', 'fall_risk', 'gds_score', 'primary_diagnosis']):
            if self.is_complete_assessment():
                self.trigger_ml_prediction()
    
    def is_complete_assessment(self):
        """Check if assessment has all required fields for ML prediction"""
        required_fields = [
            'age', 'gender', 'mmse_score', 'moca_score', 'adl_score', 'iadl_score',
            'mobility', 'fall_risk', 'gds_score', 'social_support', 'living_situation',
            'primary_diagnosis', 'comorbidities'
        ]
        
        for field in required_fields:
            if not self.get(field):
                return False
        return True
    
    def trigger_ml_prediction(self):
        """Trigger ML prediction for this assessment"""
        try:
            # Import here to avoid circular imports
            from care_placement.ml_predictor import predict_care_placement
            
            # Set prediction status to pending
            self.prediction_status = 'Pending'
            self.save()
            
            # Make prediction in background
            frappe.enqueue(
                method=predict_care_placement,
                queue='default',
                timeout=300,
                patient_assessment_name=self.name
            )
            
        except Exception as e:
            frappe.logger().error(f"Failed to trigger ML prediction: {str(e)}")
            self.prediction_status = 'Error'
            self.save()
    
    def get_prediction_summary(self):
        """Get formatted prediction summary"""
        if not self.predicted_care_type:
            return "No prediction available"
        
        care_type_display = self.predicted_care_type.replace('_', ' ').title()
        confidence = flt(self.prediction_confidence, 1)
        
        return f"{care_type_display} ({confidence}% confidence)"
    
    def get_risk_level(self):
        """Get overall risk level based on assessment"""
        if self.care_complexity_index >= 8:
            return "High Risk"
        elif self.care_complexity_index >= 5:
            return "Medium Risk"
        else:
            return "Low Risk"
    
    def get_clinical_alerts(self):
        """Get list of clinical alerts based on assessment"""
        alerts = []
        
        # Cognitive alerts
        if self.mmse_score < 15:
            alerts.append("Severe cognitive impairment detected")
        elif self.mmse_score < 20:
            alerts.append("Moderate cognitive impairment detected")
        
        # Functional alerts
        if self.adl_score <= 2:
            alerts.append("Severe functional dependency")
        elif self.adl_score <= 4:
            alerts.append("Moderate functional dependency")
        
        # Safety alerts
        if self.fall_risk == 'High':
            alerts.append("High fall risk - safety measures required")
        
        # Mental health alerts
        if self.gds_score >= 10:
            alerts.append("Severe depression symptoms")
        elif self.gds_score >= 6:
            alerts.append("Moderate depression symptoms")
        
        # Social alerts
        if self.social_support == 'Low' and self.living_situation == 'Alone':
            alerts.append("Limited social support - isolation risk")
        
        return alerts

# Server-side methods for client-side calls

@frappe.whitelist()
def make_prediction(doc_name):
    """Manual prediction trigger from client"""
    try:
        doc = frappe.get_doc("Patient Assessment", doc_name)
        
        if not doc.is_complete_assessment():
            return {
                'success': False,
                'message': 'Assessment is incomplete. Please fill all required fields.'
            }
        
        doc.trigger_ml_prediction()
        
        return {
            'success': True,
            'message': 'Prediction initiated. Results will be available shortly.'
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': str(e)
        }

@frappe.whitelist()
def update_prediction(doc_name):
    """Update prediction for existing assessment"""
    return make_prediction(doc_name)

@frappe.whitelist()
def get_assessment_summary(doc_name):
    """Get comprehensive assessment summary"""
    try:
        doc = frappe.get_doc("Patient Assessment", doc_name)
        
        return {
            'success': True,
            'summary': {
                'prediction': doc.get_prediction_summary(),
                'risk_level': doc.get_risk_level(),
                'alerts': doc.get_clinical_alerts(),
                'complexity_score': doc.care_complexity_index,
                'functional_dependency': doc.functional_dependency,
                'priority': doc.priority
            }
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': str(e)
        }

@frappe.whitelist()
def get_similar_cases(doc_name, limit=5):
    """Find similar patient cases for comparison"""
    try:
        doc = frappe.get_doc("Patient Assessment", doc_name)
        
        # Find similar cases based on age, MMSE, and care complexity
        similar_cases = frappe.db.sql("""
            SELECT name, patient_name, age, mmse_score, predicted_care_type, 
                   prediction_confidence, care_complexity_index
            FROM `tabPatient Assessment`
            WHERE name != %(current_doc)s
            AND predicted_care_type IS NOT NULL
            AND ABS(age - %(age)s) <= 5
            AND ABS(mmse_score - %(mmse)s) <= 3
            AND ABS(care_complexity_index - %(complexity)s) <= 2
            ORDER BY 
                ABS(age - %(age)s) + 
                ABS(mmse_score - %(mmse)s) + 
                ABS(care_complexity_index - %(complexity)s)
            LIMIT %(limit)s
        """, {
            'current_doc': doc_name,
            'age': doc.age,
            'mmse': doc.mmse_score,
            'complexity': doc.care_complexity_index,
            'limit': limit
        }, as_dict=True)
        
        return {
            'success': True,
            'similar_cases': similar_cases
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': str(e)
        }
