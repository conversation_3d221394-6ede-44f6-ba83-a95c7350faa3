# -*- coding: utf-8 -*-
"""
ML Prediction Engine for Care Placement
Integrates trained models with Frappe framework
"""

import frappe
import pandas as pd
import numpy as np
import joblib
import os
from frappe import _
from frappe.utils import now_datetime, flt
import json

class MLPredictor:
    """Machine Learning Prediction Engine"""
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.label_encoder = None
        self.feature_names = None
        self.feature_importance = None
        self.model_loaded = False
        self.load_models()
    
    def get_model_path(self, filename):
        """Get full path to model file"""
        app_path = frappe.get_app_path("care_placement")
        return os.path.join(app_path, "models", filename)
    
    def load_models(self):
        """Load all ML models and components"""
        try:
            self.model = joblib.load(self.get_model_path("best_model.pkl"))
            self.scaler = joblib.load(self.get_model_path("scaler.pkl"))
            self.label_encoder = joblib.load(self.get_model_path("label_encoder.pkl"))
            self.feature_names = joblib.load(self.get_model_path("feature_names.pkl"))
            self.feature_importance = joblib.load(self.get_model_path("feature_importance.pkl"))
            self.model_loaded = True
            frappe.logger().info("ML models loaded successfully")
        except Exception as e:
            frappe.logger().error(f"Failed to load ML models: {str(e)}")
            self.model_loaded = False
    
    def calculate_derived_features(self, patient_data):
        """Calculate derived features like in training"""
        # Functional Dependency Score
        functional_dependency = (6 - patient_data.get('adl_score', 6)) + (8 - patient_data.get('iadl_score', 8))
        
        # Care Complexity Index
        complexity_score = 0
        if patient_data.get('mmse_score', 30) < 20:
            complexity_score += 2
        if patient_data.get('adl_score', 6) < 4:
            complexity_score += 2
        if patient_data.get('fall_risk') == 'High':
            complexity_score += 2
        if patient_data.get('comorbidities', 0) > 3:
            complexity_score += 2
        if patient_data.get('social_support') == 'Low':
            complexity_score += 1
        
        patient_data['functional_dependency'] = functional_dependency
        patient_data['care_complexity_index'] = complexity_score
        
        return patient_data
    
    def preprocess_patient_data(self, patient_data):
        """Convert patient data to model input format"""
        if not self.model_loaded:
            raise Exception("ML models not loaded")
        
        # Calculate derived features
        patient_data = self.calculate_derived_features(patient_data)
        
        # Create DataFrame
        input_df = pd.DataFrame([patient_data])
        
        # Handle categorical variables
        categorical_features = ['gender', 'mobility', 'fall_risk', 'social_support', 
                              'living_situation', 'primary_diagnosis']
        
        for feature in categorical_features:
            if feature in input_df.columns:
                dummies = pd.get_dummies(input_df[feature], prefix=feature.title())
                input_df = pd.concat([input_df, dummies], axis=1)
                input_df.drop(feature, axis=1, inplace=True)
        
        # Ensure all required features are present
        for feature in self.feature_names:
            if feature not in input_df.columns:
                input_df[feature] = 0
        
        # Select only training features
        input_df = input_df[self.feature_names]
        
        return input_df
    
    def predict_care_placement(self, patient_data):
        """Make care placement prediction"""
        try:
            if not self.model_loaded:
                return {
                    'success': False,
                    'error': 'ML models not loaded'
                }
            
            # Preprocess input
            input_df = self.preprocess_patient_data(patient_data)
            
            # Scale features
            input_scaled = self.scaler.transform(input_df)
            
            # Make prediction
            prediction = self.model.predict(input_scaled)
            probabilities = self.model.predict_proba(input_scaled)
            
            # Get prediction details
            predicted_class = self.label_encoder.inverse_transform([prediction[0]])[0]
            confidence = float(max(probabilities[0]))
            
            # Create probability dictionary
            prob_dict = {}
            for i, class_name in enumerate(self.label_encoder.classes_):
                prob_dict[class_name] = float(probabilities[0][i])
            
            # Get top contributing features
            feature_contributions = self.get_feature_contributions(input_df.iloc[0])
            
            return {
                'success': True,
                'prediction': predicted_class,
                'confidence': confidence,
                'probabilities': prob_dict,
                'feature_contributions': feature_contributions,
                'model_version': '1.0.0',
                'prediction_date': now_datetime()
            }
            
        except Exception as e:
            frappe.logger().error(f"Prediction error: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_feature_contributions(self, patient_features):
        """Get top contributing features for this prediction"""
        try:
            # Get feature importance scores
            importance_dict = dict(zip(self.feature_names, self.model.feature_importances_))
            
            # Get patient's feature values
            contributions = []
            for feature in self.feature_names[:10]:  # Top 10 features
                if feature in self.feature_importance['Feature'].values:
                    importance = importance_dict.get(feature, 0)
                    value = patient_features.get(feature, 0)
                    contributions.append({
                        'feature': feature,
                        'importance': float(importance),
                        'value': float(value)
                    })
            
            return sorted(contributions, key=lambda x: x['importance'], reverse=True)[:5]
            
        except Exception as e:
            frappe.logger().error(f"Feature contribution error: {str(e)}")
            return []

# Global predictor instance
_predictor = None

def get_predictor():
    """Get global predictor instance"""
    global _predictor
    if _predictor is None:
        _predictor = MLPredictor()
    return _predictor

@frappe.whitelist()
def predict_care_placement(patient_assessment_name):
    """API endpoint for care placement prediction"""
    try:
        # Get patient assessment document
        doc = frappe.get_doc("Patient Assessment", patient_assessment_name)
        
        # Prepare patient data
        patient_data = {
            'age': doc.age,
            'gender': doc.gender,
            'mmse_score': doc.mmse_score,
            'moca_score': doc.moca_score,
            'adl_score': doc.adl_score,
            'iadl_score': doc.iadl_score,
            'mobility': doc.mobility,
            'fall_risk': doc.fall_risk,
            'gds_score': doc.gds_score,
            'social_support': doc.social_support,
            'living_situation': doc.living_situation,
            'primary_diagnosis': doc.primary_diagnosis,
            'comorbidities': doc.comorbidities
        }
        
        # Make prediction
        predictor = get_predictor()
        result = predictor.predict_care_placement(patient_data)
        
        if result['success']:
            # Update patient assessment with results
            doc.prediction_status = 'Completed'
            doc.predicted_care_type = result['prediction']
            doc.prediction_confidence = result['confidence'] * 100
            doc.prediction_date = result['prediction_date']
            doc.model_version = result['model_version']
            doc.save()
            
            # Create prediction record
            create_prediction_record(doc, result)
            
            frappe.db.commit()
            
            return {
                'success': True,
                'message': 'Prediction completed successfully',
                'data': result
            }
        else:
            doc.prediction_status = 'Error'
            doc.save()
            frappe.db.commit()
            
            return {
                'success': False,
                'message': result.get('error', 'Prediction failed')
            }
            
    except Exception as e:
        frappe.logger().error(f"API prediction error: {str(e)}")
        return {
            'success': False,
            'message': str(e)
        }

def create_prediction_record(assessment_doc, prediction_result):
    """Create a separate prediction record for tracking"""
    try:
        prediction_doc = frappe.get_doc({
            'doctype': 'Care Placement Prediction',
            'patient_assessment': assessment_doc.name,
            'patient_name': assessment_doc.patient_name,
            'prediction_date': prediction_result['prediction_date'],
            'predicted_care_type': prediction_result['prediction'],
            'confidence_score': prediction_result['confidence'] * 100,
            'model_version': prediction_result['model_version'],
            'probabilities': json.dumps(prediction_result['probabilities']),
            'feature_contributions': json.dumps(prediction_result['feature_contributions']),
            'status': 'Active'
        })
        prediction_doc.insert()
        
    except Exception as e:
        frappe.logger().error(f"Failed to create prediction record: {str(e)}")

@frappe.whitelist()
def batch_predict(patient_assessments):
    """Batch prediction for multiple patients"""
    try:
        if isinstance(patient_assessments, str):
            patient_assessments = json.loads(patient_assessments)
        
        results = []
        predictor = get_predictor()
        
        for assessment_name in patient_assessments:
            result = predict_care_placement(assessment_name)
            results.append({
                'assessment': assessment_name,
                'result': result
            })
        
        return {
            'success': True,
            'message': f'Batch prediction completed for {len(results)} patients',
            'results': results
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': str(e)
        }

@frappe.whitelist()
def get_model_info():
    """Get information about the loaded ML model"""
    try:
        predictor = get_predictor()

        if not predictor.model_loaded:
            return {
                'success': False,
                'message': 'Models not loaded'
            }

        return {
            'success': True,
            'model_info': {
                'model_type': type(predictor.model).__name__,
                'feature_count': len(predictor.feature_names),
                'classes': list(predictor.label_encoder.classes_),
                'model_loaded': predictor.model_loaded,
                'version': '1.0.0'
            }
        }

    except Exception as e:
        return {
            'success': False,
            'message': str(e)
        }

@frappe.whitelist(allow_guest=True, methods=["GET", "POST"])
def predict_web():
    """Web endpoint for direct ML predictions from the index page"""
    try:
        # Log request details for debugging
        frappe.logger().info(f"Request method: {frappe.request.method}")
        frappe.logger().info(f"Content type: {frappe.request.content_type}")

        # Get data from request - handle both GET and POST
        form_data = frappe.form_dict

        # If it's a POST request with JSON, try to parse it
        if frappe.request.method == 'POST' and frappe.request.content_type and 'application/json' in frappe.request.content_type:
            try:
                raw_data = frappe.request.get_data()
                if raw_data:
                    json_data = json.loads(raw_data)
                    form_data.update(json_data)
            except:
                pass  # Fall back to form_dict

        frappe.logger().info(f"Parsed form data: {form_data}")

        # Validate required fields
        required_fields = [
            'age', 'gender', 'mmse_score', 'moca_score', 'adl_score', 'iadl_score',
            'mobility', 'fall_risk', 'gds_score', 'social_support', 'living_situation',
            'primary_diagnosis', 'comorbidities'
        ]

        missing_fields = []
        for field in required_fields:
            if field not in form_data or form_data[field] == '' or form_data[field] is None:
                missing_fields.append(field)

        frappe.logger().info(f"Missing fields: {missing_fields}")

        if missing_fields:
            return {
                'success': False,
                'error': f'Missing required fields: {", ".join(missing_fields)}. Received fields: {list(form_data.keys())}'
            }

        # Convert form data to proper types
        try:
            patient_data = {
                'age': int(form_data.get('age')),
                'gender': form_data.get('gender'),
                'mmse_score': int(form_data.get('mmse_score')),
                'moca_score': int(form_data.get('moca_score')),
                'adl_score': int(form_data.get('adl_score')),
                'iadl_score': int(form_data.get('iadl_score')),
                'mobility': form_data.get('mobility'),
                'fall_risk': form_data.get('fall_risk'),
                'gds_score': int(form_data.get('gds_score')),
                'social_support': form_data.get('social_support'),
                'living_situation': form_data.get('living_situation'),
                'primary_diagnosis': form_data.get('primary_diagnosis'),
                'comorbidities': int(form_data.get('comorbidities'))
            }
        except (ValueError, TypeError) as e:
            return {
                'success': False,
                'error': f'Invalid data format: {str(e)}'
            }

        # Validate numeric ranges
        validation_errors = []

        if patient_data['age'] < 18 or patient_data['age'] > 120:
            validation_errors.append("Age must be between 18 and 120")
        if patient_data['mmse_score'] < 0 or patient_data['mmse_score'] > 30:
            validation_errors.append("MMSE Score must be between 0 and 30")
        if patient_data['moca_score'] < 0 or patient_data['moca_score'] > 30:
            validation_errors.append("MoCA Score must be between 0 and 30")
        if patient_data['adl_score'] < 0 or patient_data['adl_score'] > 6:
            validation_errors.append("ADL Score must be between 0 and 6")
        if patient_data['iadl_score'] < 0 or patient_data['iadl_score'] > 8:
            validation_errors.append("IADL Score must be between 0 and 8")
        if patient_data['gds_score'] < 0 or patient_data['gds_score'] > 15:
            validation_errors.append("GDS Score must be between 0 and 15")
        if patient_data['comorbidities'] < 0:
            validation_errors.append("Number of comorbidities cannot be negative")

        if validation_errors:
            return {
                'success': False,
                'error': f'Validation errors: {", ".join(validation_errors)}'
            }

        # Make prediction
        predictor = get_predictor()
        result = predictor.predict_care_placement(patient_data)

        if not result['success']:
            return {
                'success': False,
                'error': result.get('error', 'Prediction failed')
            }

        # Return successful prediction
        return {
            'success': True,
            'prediction': result['prediction'],
            'confidence': result['confidence'],
            'probabilities': result['probabilities'],
            'feature_contributions': result.get('feature_contributions', []),
            'model_version': result.get('model_version', '1.0.0'),
            'prediction_date': result['prediction_date'].isoformat() if result.get('prediction_date') else now_datetime().isoformat()
        }

    except Exception as e:
        frappe.logger().error(f"Web prediction error: {str(e)}")
        return {
            'success': False,
            'error': f'An unexpected error occurred: {str(e)}'
        }

@frappe.whitelist(allow_guest=True, methods=["GET", "POST"])
def test_api():
    """Simple test endpoint to verify API connectivity"""
    return {
        'success': True,
        'message': 'API is working!',
        'timestamp': now_datetime().isoformat(),
        'method': frappe.request.method,
        'form_data': dict(frappe.form_dict)
    }
