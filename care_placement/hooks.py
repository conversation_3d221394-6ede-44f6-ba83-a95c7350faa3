# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from . import __version__ as app_version

app_name = "care_placement"
app_title = "Care Placement"
app_publisher = "Taiwo Akinosho"
app_description = "Care Placement"
app_email = "<EMAIL>"
app_license = "mit"


# Includes in <head>
# ------------------

# include js, css files in header of desk.html
# app_include_css = "/assets/care_placement/css/care_placement.css"
# app_include_js = "/assets/care_placement/js/care_placement.js"

# include js, css files in header of web template
# web_include_css = "/assets/care_placement/css/care_placement.css"
# web_include_js = "/assets/care_placement/js/care_placement.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "care_placement/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"page" : "public/js/file.js"}

# include js in doctype views
doctype_js = {
    "Patient Assessment": "public/js/patient_assessment.js",
    "Care Placement Prediction": "public/js/care_placement_prediction.js"
}
# doctype_list_js = {"doctype" : "public/js/doctype_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
#	"Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Installation
# ------------

# before_install = "care_placement.install.before_install"
# after_install = "care_placement.install.after_install"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "care_placement.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
# 	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
# 	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
# 	"ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

doc_events = {
    "Patient Assessment": {
        "after_insert": "care_placement.care_placement.doctype.patient_assessment.patient_assessment.make_prediction",
        "on_update": "care_placement.care_placement.doctype.patient_assessment.patient_assessment.update_prediction"
    }
}

# Scheduled Tasks
# ---------------

scheduler_events = {
    "daily": [
        "care_placement.tasks.daily_model_performance_check"
    ],
    "weekly": [
        "care_placement.tasks.weekly_prediction_report"
    ]
}

# Testing
# -------

# before_tests = "care_placement.install.before_tests"

# Overriding Methods
# ------------------------------
#
# override_whitelisted_methods = {
# 	"frappe.desk.doctype.event.event.get_events": "care_placement.event.get_events"
# }
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "care_placement.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]


# User Data Protection
# --------------------

user_data_fields = [
    {
        "doctype": "Patient Assessment",
        "filter_by": "owner",
        "redact_fields": ["patient_name", "contact_number"],
        "partial": 1,
    },
]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"care_placement.auth.validate"
# ]

# API Whitelist
# -------------

# Whitelisted API methods for external access
api_whitelist = [
    "care_placement.ml_predictor.predict_care_placement",
    "care_placement.ml_predictor.batch_predict",
    "care_placement.ml_predictor.get_model_info"
]
