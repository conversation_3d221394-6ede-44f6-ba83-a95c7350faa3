<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Care Placement Prediction System</title>
    <script>
        // Get CSRF token for Frappe API calls
        window.frappe = window.frappe || {};
        frappe.csrf_token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        .form-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .form-section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #34495e;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .predict-btn {
            background-color: #3498db;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
        }
        .predict-btn:hover {
            background-color: #2980b9;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        .result.independent { background-color: #e6f3ff; border-left: 4px solid #4472C4; }
        .result.home-care { background-color: #e6f7e6; border-left: 4px solid #70AD47; }
        .result.assisted { background-color: #fff0e6; border-left: 4px solid #FF8C00; }
        .result.memory { background-color: #f0e6ff; border-left: 4px solid #9966CC; }
        .loading {
            text-align: center;
            display: none;
        }
        .probabilities {
            margin-top: 15px;
        }
        .prob-bar {
            margin: 5px 0;
            background-color: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
        }
        .prob-fill {
            height: 25px;
            line-height: 25px;
            text-align: center;
            color: white;
            font-weight: bold;
            transition: width 0.5s ease;
        }

        .score-badge {
            display: inline-block;
            padding: 6px 12px;
            margin: 3px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            border: 1px solid #ddd;
            background-color: #f8f9fa;
            color: #333;
        }

        .rec-category {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 3px solid #666;
            margin-bottom: 15px;
        }

        .rec-category h4 {
            margin-top: 0;
            color: #333;
            font-size: 16px;
        }

        .rec-category ul {
            margin-bottom: 0;
        }

        .rec-category li {
            margin-bottom: 5px;
        }

        .patient-analysis {
            text-align: left;
        }

        .analysis-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .assessment-scores {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Care Placement Prediction System</h1>
            <p>AI-powered tool to assist in elderly care placement decisions</p>
        </div>

        <form id="predictionForm">
            <div class="form-grid">
                <div class="form-section">
                    <h3>📋 Demographics</h3>
                    <div class="form-group">
                        <label for="age">Age:</label>
                        <input type="number" id="age" name="age" min="65" max="100" value="75" required>
                    </div>
                    <div class="form-group">
                        <label for="gender">Gender:</label>
                        <select id="gender" name="gender" required>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                        </select>
                    </div>

                    <h3>🧠 Cognitive Assessment</h3>
                    <div class="form-group">
                        <label for="mmse_score">MMSE Score (0-30):</label>
                        <input type="number" id="mmse_score" name="mmse_score" min="0" max="30" value="24" required>
                    </div>
                    <div class="form-group">
                        <label for="moca_score">MoCA Score (0-30):</label>
                        <input type="number" id="moca_score" name="moca_score" min="0" max="30" value="26" required>
                    </div>

                    <h3>🏃 Functional Assessment</h3>
                    <div class="form-group">
                        <label for="adl_score">ADL Score (0-6):</label>
                        <input type="number" id="adl_score" name="adl_score" min="0" max="6" value="6" required>
                    </div>
                    <div class="form-group">
                        <label for="iadl_score">IADL Score (0-8):</label>
                        <input type="number" id="iadl_score" name="iadl_score" min="0" max="8" value="8" required>
                    </div>
                </div>

                <div class="form-section">
                    <h3>💪 Physical Health</h3>
                    <div class="form-group">
                        <label for="mobility">Mobility:</label>
                        <select id="mobility" name="mobility" required>
                            <option value="Independent">Independent</option>
                            <option value="Walker">Walker</option>
                            <option value="Wheelchair">Wheelchair</option>
                            <option value="Bedbound">Bedbound</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="fall_risk">Fall Risk:</label>
                        <select id="fall_risk" name="fall_risk" required>
                            <option value="Low">Low</option>
                            <option value="Medium">Medium</option>
                            <option value="High">High</option>
                        </select>
                    </div>

                    <h3>🧘 Mental Health</h3>
                    <div class="form-group">
                        <label for="gds_score">GDS Score (0-15):</label>
                        <input type="number" id="gds_score" name="gds_score" min="0" max="15" value="5" required>
                    </div>

                    <h3>👥 Social Support</h3>
                    <div class="form-group">
                        <label for="social_support">Social Support Level:</label>
                        <select id="social_support" name="social_support" required>
                            <option value="Low">Low</option>
                            <option value="Medium">Medium</option>
                            <option value="High">High</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="living_situation">Living Situation:</label>
                        <select id="living_situation" name="living_situation" required>
                            <option value="Alone">Alone</option>
                            <option value="With_Family">With Family</option>
                            <option value="With_Spouse">With Spouse</option>
                        </select>
                    </div>

                    <h3>⚕️ Medical Information</h3>
                    <div class="form-group">
                        <label for="primary_diagnosis">Primary Diagnosis:</label>
                        <select id="primary_diagnosis" name="primary_diagnosis" required>
                            <option value="Dementia">Dementia</option>
                            <option value="Stroke">Stroke</option>
                            <option value="Heart_Disease">Heart Disease</option>
                            <option value="Diabetes">Diabetes</option>
                            <option value="COPD">COPD</option>
                            <option value="Arthritis">Arthritis</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="comorbidities">Number of Comorbidities:</label>
                        <input type="number" id="comorbidities" name="comorbidities" min="0" max="10" value="2" required>
                    </div>
                </div>
            </div>

            <button type="submit" class="predict-btn">🔮 Predict Care Placement</button>
            <button type="button" class="predict-btn" onclick="testAPI()" style="background-color: #6c757d; margin-left: 10px;">🔧 Test API</button>
            <button type="button" class="predict-btn" onclick="debugModels()" style="background-color: #dc3545; margin-left: 10px;">🐛 Debug Models</button>
        </form>

        <div class="loading" id="loading">
            <h3>🔄 Analyzing patient data...</h3>
        </div>

        <div class="result" id="result">
            <h3 id="prediction-title">🎯 Prediction Result</h3>
            <p id="prediction-text"></p>
            <div class="probabilities" id="probabilities"></div>
        </div>
    </div>

    <script>
        document.getElementById('predictionForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
            
            // Get form data
            const formData = new FormData(this);
            
            try {
                // Convert FormData to URL parameters to avoid CSRF issues
                const params = new URLSearchParams();
                for (let [key, value] of formData.entries()) {
                    params.append(key, value);
                }

                console.log('Sending data as URL params:', params.toString());

                const response = await fetch('/api/method/care_placement.ml_predictor.predict_web?' + params.toString(), {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                console.log('Response status:', response.status);

                const result = await response.json();
                console.log('Full response:', result);

                if (!response.ok) {
                    const errorMsg = result.message || result.error || `HTTP error! status: ${response.status}`;
                    throw new Error(errorMsg);
                }

                const data = result.message || result;

                if (!data.success) {
                    alert('Error: ' + (data.error || 'Prediction failed'));
                    return;
                }

                // Display results
                displayResults(data);
                
            } catch (error) {
                alert('Error: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        });
        
        function displayResults(data) {
            const resultDiv = document.getElementById('result');
            const predictionText = document.getElementById('prediction-text');
            const probabilitiesDiv = document.getElementById('probabilities');

            // Get form data for patient profile
            const formData = new FormData(document.getElementById('predictionForm'));
            const patientProfile = {};
            for (let [key, value] of formData.entries()) {
                patientProfile[key] = value;
            }

            // Set prediction text with comprehensive analysis
            const confidencePercentage = (data.confidence * 100).toFixed(1);
            predictionText.innerHTML = createPatientAnalysis(patientProfile, data, confidencePercentage);

            // Set result class for styling
            resultDiv.className = 'result';
            if (data.prediction.includes('Independent')) {
                resultDiv.classList.add('independent');
            } else if (data.prediction.includes('Home')) {
                resultDiv.classList.add('home-care');
            } else if (data.prediction.includes('Assisted')) {
                resultDiv.classList.add('assisted');
            } else if (data.prediction.includes('Memory')) {
                resultDiv.classList.add('memory');
            }

            // Create probability bars
            probabilitiesDiv.innerHTML = '<h4 style="color: #333; margin-bottom: 15px;">📊 Prediction Confidence:</h4>';

            for (const [careType, probability] of Object.entries(data.probabilities)) {
                const percentage = (probability * 100).toFixed(1);
                const probBar = document.createElement('div');
                probBar.style.cssText = 'margin: 8px 0; padding: 10px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px;';
                probBar.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-weight: 500; color: #333;">${careType}</span>
                        <span style="font-weight: bold; color: #333;">${percentage}%</span>
                    </div>
                    <div style="background: #e9ecef; height: 8px; border-radius: 4px; margin-top: 5px; overflow: hidden;">
                        <div style="background: #6c757d; height: 100%; width: ${percentage}%; transition: width 0.3s ease;"></div>
                    </div>
                `;
                probabilitiesDiv.appendChild(probBar);
            }

            // Add feature contributions if available
            if (data.feature_contributions && data.feature_contributions.length > 0) {
                const contributionsDiv = document.createElement('div');
                contributionsDiv.innerHTML = '<h4 style="margin-top: 25px; color: #333; margin-bottom: 15px;">🔍 Key Contributing Factors:</h4>';

                data.feature_contributions.slice(0, 5).forEach(contrib => {
                    const importance = (contrib.importance * 100).toFixed(1);
                    const factorDiv = document.createElement('div');
                    factorDiv.style.cssText = 'margin: 8px 0; padding: 10px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;';
                    factorDiv.innerHTML = `<strong style="color: #333;">${formatFeatureName(contrib.feature)}:</strong> <span style="color: #666;">${importance}% importance (value: ${contrib.value})</span>`;
                    contributionsDiv.appendChild(factorDiv);
                });

                probabilitiesDiv.appendChild(contributionsDiv);
            }

            // Show result
            resultDiv.style.display = 'block';
        }

        function createPatientAnalysis(profile, prediction, confidence) {
            const careType = prediction.prediction.replace(/_/g, ' ');

            return `
                <div class="patient-analysis">
                    <div class="analysis-header">
                        <h2 style="color: #333; margin: 0 0 15px 0;">🎯 Care Placement Recommendation</h2>
                        <div class="recommendation-badge" style="background: #f8f9fa; color: #333; padding: 12px 24px; border: 2px solid #333; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 18px;">
                            ${careType}
                        </div>
                        <div style="margin-top: 15px; color: #666; font-size: 14px;">
                            <strong>Confidence:</strong> ${confidence}% | <strong>Model:</strong> ${prediction.model_version || 'v1.0.0'}
                        </div>
                    </div>

                    <div class="patient-profile" style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; border: 1px solid #ddd;">
                        <h3 style="color: #333; margin-top: 0;">👤 Patient Profile</h3>
                        <div class="profile-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div><strong>Age:</strong> ${profile.age} years old</div>
                            <div><strong>Gender:</strong> ${profile.gender}</div>
                            <div><strong>Mobility:</strong> ${profile.mobility}</div>
                            <div><strong>Fall Risk:</strong> ${profile.fall_risk}</div>
                            <div><strong>Living Situation:</strong> ${profile.living_situation}</div>
                            <div><strong>Social Support:</strong> ${profile.social_support}</div>
                        </div>

                        <div style="margin-top: 15px;">
                            <h4 style="color: #333; margin-bottom: 10px;">🧠 Cognitive Assessment</h4>
                            <div class="assessment-scores">
                                <span class="score-badge ${getScoreClass('mmse', profile.mmse_score)}">MMSE: ${profile.mmse_score}/30 ${interpretMMSE(profile.mmse_score)}</span>
                                <span class="score-badge ${getScoreClass('moca', profile.moca_score)}">MoCA: ${profile.moca_score}/30 ${interpretMoCA(profile.moca_score)}</span>
                                <span class="score-badge ${getScoreClass('gds', profile.gds_score)}">GDS: ${profile.gds_score}/15 ${interpretGDS(profile.gds_score)}</span>
                            </div>
                        </div>

                        <div style="margin-top: 15px;">
                            <h4 style="color: #333; margin-bottom: 10px;">🏃 Functional Assessment</h4>
                            <div class="assessment-scores">
                                <span class="score-badge ${getScoreClass('adl', profile.adl_score)}">ADL: ${profile.adl_score}/6 ${interpretADL(profile.adl_score)}</span>
                                <span class="score-badge ${getScoreClass('iadl', profile.iadl_score)}">IADL: ${profile.iadl_score}/8 ${interpretIADL(profile.iadl_score)}</span>
                            </div>
                        </div>

                        <div style="margin-top: 15px;">
                            <div><strong>Primary Diagnosis:</strong> ${profile.primary_diagnosis}</div>
                            <div><strong>Comorbidities:</strong> ${profile.comorbidities} conditions</div>
                        </div>
                    </div>

                    <div class="care-reasoning" style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; border: 1px solid #ddd;">
                        <h3 style="color: #333; margin-top: 0;">💡 Clinical Reasoning</h3>
                        ${generateCareReasoning(profile, prediction.prediction)}
                    </div>

                    <div class="recommendations" style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; border: 1px solid #ddd;">
                        <h3 style="color: #333; margin-top: 0;">📋 Recommended Services & Interventions</h3>
                        ${generateRecommendations(profile, prediction.prediction)}
                    </div>
                </div>
            `;
        }

        function formatFeatureName(featureName) {
            const nameMap = {
                'mmse_score': 'MMSE Score',
                'adl_score': 'ADL Score',
                'iadl_score': 'IADL Score',
                'gds_score': 'GDS Score',
                'moca_score': 'MoCA Score',
                'functional_dependency': 'Functional Dependency',
                'care_complexity_index': 'Care Complexity',
                'age': 'Age',
                'comorbidities': 'Comorbidities'
            };
            return nameMap[featureName] || featureName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        function getCareTypeColor(careType) {
            const colors = {
                'Independent_Living': '#4CAF50',
                'Home_Care': '#2196F3',
                'Assisted_Living': '#FF9800',
                'Memory_Care': '#9C27B0',
                'Skilled_Nursing': '#F44336'
            };
            return colors[careType] || '#757575';
        }

        function getScoreClass(type, score) {
            if (type === 'mmse') {
                if (score >= 24) return 'score-good';
                if (score >= 18) return 'score-moderate';
                return 'score-poor';
            }
            if (type === 'moca') {
                if (score >= 26) return 'score-good';
                if (score >= 18) return 'score-moderate';
                return 'score-poor';
            }
            if (type === 'adl') {
                if (score >= 5) return 'score-good';
                if (score >= 3) return 'score-moderate';
                return 'score-poor';
            }
            if (type === 'iadl') {
                if (score >= 6) return 'score-good';
                if (score >= 4) return 'score-moderate';
                return 'score-poor';
            }
            if (type === 'gds') {
                if (score <= 4) return 'score-good';
                if (score <= 9) return 'score-moderate';
                return 'score-poor';
            }
            return 'score-neutral';
        }

        function interpretMMSE(score) {
            if (score >= 24) return '(Normal)';
            if (score >= 18) return '(Mild Impairment)';
            if (score >= 10) return '(Moderate Impairment)';
            return '(Severe Impairment)';
        }

        function interpretMoCA(score) {
            if (score >= 26) return '(Normal)';
            if (score >= 18) return '(Mild Impairment)';
            return '(Significant Impairment)';
        }

        function interpretADL(score) {
            if (score >= 5) return '(Independent)';
            if (score >= 3) return '(Needs Some Help)';
            return '(Dependent)';
        }

        function interpretIADL(score) {
            if (score >= 6) return '(Independent)';
            if (score >= 4) return '(Needs Some Help)';
            return '(Dependent)';
        }

        function interpretGDS(score) {
            if (score <= 4) return '(Normal)';
            if (score <= 9) return '(Mild Depression)';
            return '(Severe Depression)';
        }

        function generateCareReasoning(profile, careType) {
            const age = parseInt(profile.age);
            const mmse = parseInt(profile.mmse_score);
            const adl = parseInt(profile.adl_score);
            const iadl = parseInt(profile.iadl_score);
            const gds = parseInt(profile.gds_score);

            let reasoning = `<p><strong>Clinical Assessment Summary:</strong></p><ul>`;

            // Age considerations
            if (age >= 80) {
                reasoning += `<li>Advanced age (${age} years) increases care complexity and safety concerns</li>`;
            } else if (age >= 65) {
                reasoning += `<li>Senior age (${age} years) with typical age-related care considerations</li>`;
            }

            // Cognitive assessment
            if (mmse < 18) {
                reasoning += `<li>Significant cognitive impairment (MMSE: ${mmse}/30) requires specialized memory care</li>`;
            } else if (mmse < 24) {
                reasoning += `<li>Mild cognitive impairment (MMSE: ${mmse}/30) needs cognitive support and monitoring</li>`;
            } else {
                reasoning += `<li>Cognitive function relatively preserved (MMSE: ${mmse}/30)</li>`;
            }

            // Functional assessment
            if (adl < 3) {
                reasoning += `<li>Significant functional dependency (ADL: ${adl}/6) requires extensive personal care assistance</li>`;
            } else if (adl < 5) {
                reasoning += `<li>Moderate functional limitations (ADL: ${adl}/6) needs help with daily activities</li>`;
            } else {
                reasoning += `<li>Good basic functional independence (ADL: ${adl}/6)</li>`;
            }

            if (iadl < 4) {
                reasoning += `<li>Cannot manage complex daily tasks (IADL: ${iadl}/8) - needs help with medications, finances, cooking</li>`;
            } else if (iadl < 6) {
                reasoning += `<li>Some difficulty with complex tasks (IADL: ${iadl}/8) - needs assistance with some activities</li>`;
            }

            // Mobility and safety
            if (profile.mobility !== 'Independent') {
                reasoning += `<li>Mobility limitations (${profile.mobility}) increase fall risk and care needs</li>`;
            }

            if (profile.fall_risk === 'High') {
                reasoning += `<li>High fall risk requires environmental modifications and supervision</li>`;
            }

            // Mental health
            if (gds > 9) {
                reasoning += `<li>Severe depression symptoms (GDS: ${gds}/15) require mental health intervention</li>`;
            } else if (gds > 4) {
                reasoning += `<li>Mild depression symptoms (GDS: ${gds}/15) need monitoring and support</li>`;
            }

            // Social factors
            if (profile.social_support === 'Low') {
                reasoning += `<li>Limited social support increases isolation risk and care needs</li>`;
            }

            reasoning += `</ul>`;

            // Care type specific reasoning
            reasoning += `<p><strong>Why ${careType.replace('_', ' ')}?</strong></p>`;

            switch(careType) {
                case 'Independent_Living':
                    reasoning += `<p>Patient demonstrates sufficient cognitive and functional capacity for independent living with minimal support services.</p>`;
                    break;
                case 'Home_Care':
                    reasoning += `<p>Patient can remain at home safely with professional support for specific deficits in daily functioning, particularly with complex tasks and medication management.</p>`;
                    break;
                case 'Assisted_Living':
                    reasoning += `<p>Patient requires structured environment with 24/7 availability of assistance for daily activities while maintaining some independence.</p>`;
                    break;
                case 'Memory_Care':
                    reasoning += `<p>Significant cognitive impairment requires specialized dementia care with secure environment and specialized staff training.</p>`;
                    break;
                case 'Skilled_Nursing':
                    reasoning += `<p>Complex medical needs and significant functional dependency require 24/7 skilled nursing care and medical supervision.</p>`;
                    break;
            }

            return reasoning;
        }

        function generateRecommendations(profile, careType) {
            const mmse = parseInt(profile.mmse_score);
            const adl = parseInt(profile.adl_score);
            const iadl = parseInt(profile.iadl_score);
            const gds = parseInt(profile.gds_score);

            let recommendations = '<div class="recommendations-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">';

            // Core services based on care type
            switch(careType) {
                case 'Independent_Living':
                    recommendations += `
                        <div class="rec-category">
                            <h4>🏠 Independent Living Support</h4>
                            <ul>
                                <li>Regular wellness checks</li>
                                <li>Emergency response system</li>
                                <li>Transportation services</li>
                                <li>Social activities and programs</li>
                            </ul>
                        </div>`;
                    break;

                case 'Home_Care':
                    recommendations += `
                        <div class="rec-category">
                            <h4>🏠 Home Care Services</h4>
                            <ul>
                                <li>Home health aide (${adl < 4 ? '4-6 hours/day' : '2-4 hours/day'})</li>
                                <li>Medication management and monitoring</li>
                                <li>Meal preparation and delivery</li>
                                <li>Light housekeeping assistance</li>
                                <li>Transportation to appointments</li>
                            </ul>
                        </div>`;
                    break;

                case 'Assisted_Living':
                    recommendations += `
                        <div class="rec-category">
                            <h4>🏢 Assisted Living Services</h4>
                            <ul>
                                <li>24/7 staff availability</li>
                                <li>Assistance with ADLs as needed</li>
                                <li>Medication administration</li>
                                <li>Three meals daily + snacks</li>
                                <li>Housekeeping and laundry</li>
                                <li>Social and recreational activities</li>
                            </ul>
                        </div>`;
                    break;

                case 'Memory_Care':
                    recommendations += `
                        <div class="rec-category">
                            <h4>🧠 Memory Care Services</h4>
                            <ul>
                                <li>Secure, dementia-specific environment</li>
                                <li>Specialized dementia care staff</li>
                                <li>Structured daily routines</li>
                                <li>Cognitive stimulation programs</li>
                                <li>Behavioral management support</li>
                                <li>Family education and support</li>
                            </ul>
                        </div>`;
                    break;

                case 'Skilled_Nursing':
                    recommendations += `
                        <div class="rec-category">
                            <h4>🏥 Skilled Nursing Services</h4>
                            <ul>
                                <li>24/7 skilled nursing care</li>
                                <li>Medical management and monitoring</li>
                                <li>Physical, occupational, speech therapy</li>
                                <li>Wound care and medical procedures</li>
                                <li>Medication administration</li>
                                <li>Physician services</li>
                            </ul>
                        </div>`;
                    break;
            }

            // Additional recommendations based on specific needs
            if (mmse < 24 || iadl < 6) {
                recommendations += `
                    <div class="rec-category">
                        <h4>🧠 Cognitive Support</h4>
                        <ul>
                            <li>Cognitive assessment and monitoring</li>
                            <li>Memory aids and environmental cues</li>
                            <li>Structured daily routines</li>
                            ${mmse < 18 ? '<li>Dementia-specific interventions</li>' : ''}
                            <li>Family education on cognitive changes</li>
                        </ul>
                    </div>`;
            }

            if (profile.fall_risk === 'High' || profile.mobility !== 'Independent') {
                recommendations += `
                    <div class="rec-category">
                        <h4>⚖️ Safety & Mobility</h4>
                        <ul>
                            <li>Fall risk assessment and prevention</li>
                            <li>Home safety evaluation</li>
                            <li>Physical therapy evaluation</li>
                            <li>Assistive devices as needed</li>
                            <li>Environmental modifications</li>
                        </ul>
                    </div>`;
            }

            if (gds > 4) {
                recommendations += `
                    <div class="rec-category">
                        <h4>💚 Mental Health Support</h4>
                        <ul>
                            <li>Depression screening and monitoring</li>
                            <li>Mental health counseling</li>
                            <li>Social engagement activities</li>
                            <li>Medication review for depression</li>
                            <li>Family support and education</li>
                        </ul>
                    </div>`;
            }

            if (parseInt(profile.comorbidities) > 2) {
                recommendations += `
                    <div class="rec-category">
                        <h4>🩺 Medical Management</h4>
                        <ul>
                            <li>Comprehensive medication review</li>
                            <li>Care coordination between providers</li>
                            <li>Regular health monitoring</li>
                            <li>Chronic disease management</li>
                            <li>Preventive care scheduling</li>
                        </ul>
                    </div>`;
            }

            recommendations += '</div>';

            // Next steps
            recommendations += `
                <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 6px;">
                    <h4 style="color: #333; margin-top: 0;">📋 Next Steps</h4>
                    <ol style="color: #333; line-height: 1.6;">
                        <li>Discuss recommendations with patient and family</li>
                        <li>Conduct detailed assessment by care team</li>
                        <li>Evaluate financial resources and insurance coverage</li>
                        <li>Tour recommended care facilities or interview home care agencies</li>
                        <li>Develop transition plan with timeline</li>
                        <li>Arrange follow-up assessment in 3-6 months</li>
                    </ol>
                </div>`;

            return recommendations;
        }

        async function testAPI() {
            try {
                console.log('Testing API connectivity...');
                const response = await fetch('/api/method/care_placement.ml_predictor.test_api', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                const result = await response.json();
                console.log('Test API response:', result);

                if (response.ok && result.message && result.message.success) {
                    alert('✅ API Test Successful!\n' + result.message.message + '\nTimestamp: ' + result.message.timestamp);
                } else {
                    alert('❌ API Test Failed:\n' + JSON.stringify(result, null, 2));
                }
            } catch (error) {
                console.error('API test error:', error);
                alert('❌ API Test Error:\n' + error.message);
            }
        }

        async function debugModels() {
            try {
                console.log('Debugging model loading...');
                const response = await fetch('/api/method/care_placement.ml_predictor.debug_models', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                const result = await response.json();
                console.log('Debug models response:', result);

                if (response.ok && result.message) {
                    const debug = result.message;
                    let message = `🐛 Model Debug Info:\n\n`;
                    message += `Model Loaded: ${debug.model_loaded}\n`;
                    message += `App Path: ${debug.app_path}\n`;
                    message += `Model Path: ${debug.model_path}\n`;
                    message += `Model File Exists: ${debug.model_file_exists}\n`;
                    message += `Models Dir Exists: ${debug.models_dir_exists}\n`;
                    message += `Models Dir Contents: ${debug.models_dir_contents.join(', ')}\n`;
                    message += `Current Working Dir: ${debug.current_working_dir}`;

                    alert(message);
                } else {
                    alert('❌ Debug Failed:\n' + JSON.stringify(result, null, 2));
                }
            } catch (error) {
                console.error('Debug error:', error);
                alert('❌ Debug Error:\n' + error.message);
            }
        }
    </script>
</body>
</html>
