<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Care Placement Prediction System</title>
    <script>
        // Get CSRF token for Frappe API calls
        window.frappe = window.frappe || {};
        frappe.csrf_token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        .form-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .form-section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #34495e;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .predict-btn {
            background-color: #3498db;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
        }
        .predict-btn:hover {
            background-color: #2980b9;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        .result.independent { background-color: #e6f3ff; border-left: 4px solid #4472C4; }
        .result.home-care { background-color: #e6f7e6; border-left: 4px solid #70AD47; }
        .result.assisted { background-color: #fff0e6; border-left: 4px solid #FF8C00; }
        .result.memory { background-color: #f0e6ff; border-left: 4px solid #9966CC; }
        .loading {
            text-align: center;
            display: none;
        }
        .probabilities {
            margin-top: 15px;
        }
        .prob-bar {
            margin: 5px 0;
            background-color: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
        }
        .prob-fill {
            height: 25px;
            line-height: 25px;
            text-align: center;
            color: white;
            font-weight: bold;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Care Placement Prediction System</h1>
            <p>AI-powered tool to assist in elderly care placement decisions</p>
        </div>

        <form id="predictionForm">
            <div class="form-grid">
                <div class="form-section">
                    <h3>📋 Demographics</h3>
                    <div class="form-group">
                        <label for="age">Age:</label>
                        <input type="number" id="age" name="age" min="65" max="100" value="75" required>
                    </div>
                    <div class="form-group">
                        <label for="gender">Gender:</label>
                        <select id="gender" name="gender" required>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                        </select>
                    </div>

                    <h3>🧠 Cognitive Assessment</h3>
                    <div class="form-group">
                        <label for="mmse">MMSE Score (0-30):</label>
                        <input type="number" id="mmse" name="mmse" min="0" max="30" value="24" required>
                    </div>
                    <div class="form-group">
                        <label for="moca">MoCA Score (0-30):</label>
                        <input type="number" id="moca" name="moca" min="0" max="30" value="26" required>
                    </div>

                    <h3>🏃 Functional Assessment</h3>
                    <div class="form-group">
                        <label for="adl">ADL Score (0-6):</label>
                        <input type="number" id="adl" name="adl" min="0" max="6" value="6" required>
                    </div>
                    <div class="form-group">
                        <label for="iadl">IADL Score (0-8):</label>
                        <input type="number" id="iadl" name="iadl" min="0" max="8" value="8" required>
                    </div>
                </div>

                <div class="form-section">
                    <h3>💪 Physical Health</h3>
                    <div class="form-group">
                        <label for="mobility">Mobility:</label>
                        <select id="mobility" name="mobility" required>
                            <option value="Independent">Independent</option>
                            <option value="Walker">Walker</option>
                            <option value="Wheelchair">Wheelchair</option>
                            <option value="Bedbound">Bedbound</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="fall_risk">Fall Risk:</label>
                        <select id="fall_risk" name="fall_risk" required>
                            <option value="Low">Low</option>
                            <option value="Medium">Medium</option>
                            <option value="High">High</option>
                        </select>
                    </div>

                    <h3>🧘 Mental Health</h3>
                    <div class="form-group">
                        <label for="gds">GDS Score (0-15):</label>
                        <input type="number" id="gds" name="gds" min="0" max="15" value="5" required>
                    </div>

                    <h3>👥 Social Support</h3>
                    <div class="form-group">
                        <label for="social_support">Social Support Level:</label>
                        <select id="social_support" name="social_support" required>
                            <option value="Low">Low</option>
                            <option value="Medium">Medium</option>
                            <option value="High">High</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="living_situation">Living Situation:</label>
                        <select id="living_situation" name="living_situation" required>
                            <option value="Alone">Alone</option>
                            <option value="With_Family">With Family</option>
                            <option value="With_Spouse">With Spouse</option>
                        </select>
                    </div>

                    <h3>⚕️ Medical Information</h3>
                    <div class="form-group">
                        <label for="primary_diagnosis">Primary Diagnosis:</label>
                        <select id="primary_diagnosis" name="primary_diagnosis" required>
                            <option value="Dementia">Dementia</option>
                            <option value="Stroke">Stroke</option>
                            <option value="Heart_Disease">Heart Disease</option>
                            <option value="Diabetes">Diabetes</option>
                            <option value="COPD">COPD</option>
                            <option value="Arthritis">Arthritis</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="comorbidities">Number of Comorbidities:</label>
                        <input type="number" id="comorbidities" name="comorbidities" min="0" max="10" value="2" required>
                    </div>
                </div>
            </div>

            <button type="submit" class="predict-btn">🔮 Predict Care Placement</button>
            <button type="button" class="predict-btn" onclick="testAPI()" style="background-color: #6c757d; margin-left: 10px;">🔧 Test API</button>
        </form>

        <div class="loading" id="loading">
            <h3>🔄 Analyzing patient data...</h3>
        </div>

        <div class="result" id="result">
            <h3 id="prediction-title">🎯 Prediction Result</h3>
            <p id="prediction-text"></p>
            <div class="probabilities" id="probabilities"></div>
        </div>
    </div>

    <script>
        document.getElementById('predictionForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
            
            // Get form data
            const formData = new FormData(this);
            
            try {
                // Convert FormData to regular object for Frappe API
                const formObject = {};
                for (let [key, value] of formData.entries()) {
                    formObject[key] = value;
                }

                console.log('Sending data:', formObject);

                const response = await fetch('/api/method/care_placement.ml_predictor.predict_web', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(formObject)
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                const result = await response.json();
                console.log('Full response:', result);

                if (!response.ok) {
                    const errorMsg = result.message || result.error || `HTTP error! status: ${response.status}`;
                    throw new Error(errorMsg);
                }

                const data = result.message || result;

                if (!data.success) {
                    alert('Error: ' + (data.error || 'Prediction failed'));
                    return;
                }

                // Display results
                displayResults(data);
                
            } catch (error) {
                alert('Error: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        });
        
        function displayResults(data) {
            const resultDiv = document.getElementById('result');
            const predictionText = document.getElementById('prediction-text');
            const probabilitiesDiv = document.getElementById('probabilities');

            // Set prediction text with confidence
            const confidencePercentage = (data.confidence * 100).toFixed(1);
            predictionText.innerHTML = `
                <strong>Recommended Care Placement: ${data.prediction}</strong><br>
                <span style="color: #666; font-size: 14px;">
                    Confidence: ${confidencePercentage}% | Model: ${data.model_version || 'v1.0.0'}
                    ${data.prediction_id ? ` | ID: ${data.prediction_id}` : ''}
                </span>
            `;

            // Set result class for styling
            resultDiv.className = 'result';
            if (data.prediction.includes('Independent')) {
                resultDiv.classList.add('independent');
            } else if (data.prediction.includes('Home')) {
                resultDiv.classList.add('home-care');
            } else if (data.prediction.includes('Assisted')) {
                resultDiv.classList.add('assisted');
            } else if (data.prediction.includes('Memory')) {
                resultDiv.classList.add('memory');
            }

            // Create probability bars
            probabilitiesDiv.innerHTML = '<h4>📊 Prediction Confidence:</h4>';

            const colors = {
                'Independent Living': '#4472C4',
                'Home Care': '#70AD47',
                'Assisted Living': '#FF8C00',
                'Memory Care': '#9966CC'
            };

            for (const [careType, probability] of Object.entries(data.probabilities)) {
                const percentage = (probability * 100).toFixed(1);
                const probBar = document.createElement('div');
                probBar.className = 'prob-bar';
                probBar.innerHTML = `
                    <div class="prob-fill" style="width: ${percentage}%; background-color: ${colors[careType]}">
                        ${careType}: ${percentage}%
                    </div>
                `;
                probabilitiesDiv.appendChild(probBar);
            }

            // Add feature contributions if available
            if (data.feature_contributions && data.feature_contributions.length > 0) {
                const contributionsDiv = document.createElement('div');
                contributionsDiv.innerHTML = '<h4 style="margin-top: 20px;">🔍 Key Contributing Factors:</h4>';

                data.feature_contributions.slice(0, 5).forEach(contrib => {
                    const importance = (contrib.importance * 100).toFixed(1);
                    const factorDiv = document.createElement('div');
                    factorDiv.style.cssText = 'margin: 5px 0; padding: 5px; background: #f8f9fa; border-radius: 4px; font-size: 14px;';
                    factorDiv.innerHTML = `<strong>${formatFeatureName(contrib.feature)}:</strong> ${importance}% importance (value: ${contrib.value})`;
                    contributionsDiv.appendChild(factorDiv);
                });

                probabilitiesDiv.appendChild(contributionsDiv);
            }

            // Show result
            resultDiv.style.display = 'block';
        }

        function formatFeatureName(featureName) {
            const nameMap = {
                'mmse_score': 'MMSE Score',
                'adl_score': 'ADL Score',
                'iadl_score': 'IADL Score',
                'gds_score': 'GDS Score',
                'moca_score': 'MoCA Score',
                'functional_dependency': 'Functional Dependency',
                'care_complexity_index': 'Care Complexity',
                'age': 'Age',
                'comorbidities': 'Comorbidities'
            };
            return nameMap[featureName] || featureName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        async function testAPI() {
            try {
                console.log('Testing API connectivity...');
                const response = await fetch('/api/method/care_placement.ml_predictor.test_api', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                const result = await response.json();
                console.log('Test API response:', result);

                if (response.ok && result.message && result.message.success) {
                    alert('✅ API Test Successful!\n' + result.message.message);
                } else {
                    alert('❌ API Test Failed:\n' + JSON.stringify(result, null, 2));
                }
            } catch (error) {
                console.error('API test error:', error);
                alert('❌ API Test Error:\n' + error.message);
            }
        }
    </script>
</body>
</html>
