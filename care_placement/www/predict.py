# -*- coding: utf-8 -*-
"""
Web endpoint for direct ML predictions from the index page
Handles form data directly without requiring Patient Assessment records
"""

import frappe
import json
from frappe import _
from frappe.utils import now_datetime
from care_placement.ml_predictor import <PERSON><PERSON><PERSON>ictor

def get_context(context):
    """This function is called for web pages, but we're using this as an API endpoint"""
    pass

@frappe.whitelist(allow_guest=True, methods=["POST"])
def index():
    """
    Direct prediction endpoint for web interface
    Accepts form data and returns JSON prediction results
    """
    try:
        # Get form data - handle both JSON and form data
        if frappe.request.content_type and 'application/json' in frappe.request.content_type:
            form_data = json.loads(frappe.request.get_data())
        else:
            form_data = frappe.form_dict
        
        # Validate required fields
        required_fields = [
            'age', 'gender', 'mmse_score', 'moca_score', 'adl_score', 'iadl_score',
            'mobility', 'fall_risk', 'gds_score', 'social_support', 'living_situation',
            'primary_diagnosis', 'comorbidities'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in form_data or not form_data[field]:
                missing_fields.append(field)
        
        if missing_fields:
            frappe.response['http_status_code'] = 400
            return {
                'success': False,
                'error': f'Missing required fields: {", ".join(missing_fields)}'
            }
        
        # Convert form data to proper types
        patient_data = {
            'age': int(form_data.get('age')),
            'gender': form_data.get('gender'),
            'mmse_score': int(form_data.get('mmse_score')),
            'moca_score': int(form_data.get('moca_score')),
            'adl_score': int(form_data.get('adl_score')),
            'iadl_score': int(form_data.get('iadl_score')),
            'mobility': form_data.get('mobility'),
            'fall_risk': form_data.get('fall_risk'),
            'gds_score': int(form_data.get('gds_score')),
            'social_support': form_data.get('social_support'),
            'living_situation': form_data.get('living_situation'),
            'primary_diagnosis': form_data.get('primary_diagnosis'),
            'comorbidities': int(form_data.get('comorbidities'))
        }
        
        # Validate numeric ranges
        validation_errors = validate_assessment_data(patient_data)
        if validation_errors:
            frappe.response['http_status_code'] = 400
            return {
                'success': False,
                'error': f'Validation errors: {", ".join(validation_errors)}'
            }
        
        # Make prediction
        predictor = MLPredictor()
        result = predictor.predict_care_placement(patient_data)
        
        if not result['success']:
            frappe.response['http_status_code'] = 500
            return {
                'success': False,
                'error': result.get('error', 'Prediction failed')
            }
        
        # Optionally save prediction result (without creating Patient Assessment)
        prediction_id = None
        if frappe.db.exists("DocType", "Care Placement Prediction"):
            try:
                prediction_id = save_web_prediction(patient_data, result)
            except Exception as e:
                # Don't fail the prediction if saving fails
                frappe.logger().error(f"Failed to save web prediction: {str(e)}")
        
        # Return successful prediction
        response_data = {
            'success': True,
            'prediction': result['prediction'],
            'confidence': result['confidence'],
            'probabilities': result['probabilities'],
            'feature_contributions': result.get('feature_contributions', []),
            'model_version': result.get('model_version', '1.0.0'),
            'prediction_date': result['prediction_date'].isoformat() if result.get('prediction_date') else now_datetime().isoformat()
        }
        
        if prediction_id:
            response_data['prediction_id'] = prediction_id
        
        return response_data
        
    except ValueError as e:
        frappe.response['http_status_code'] = 400
        return {
            'success': False,
            'error': f'Invalid data format: {str(e)}'
        }
    except Exception as e:
        frappe.logger().error(f"Web prediction error: {str(e)}")
        frappe.response['http_status_code'] = 500
        return {
            'success': False,
            'error': 'An unexpected error occurred. Please try again.'
        }

def validate_assessment_data(data):
    """Validate assessment data ranges"""
    errors = []
    
    # Age validation
    if data['age'] < 18 or data['age'] > 120:
        errors.append("Age must be between 18 and 120")
    
    # MMSE Score validation
    if data['mmse_score'] < 0 or data['mmse_score'] > 30:
        errors.append("MMSE Score must be between 0 and 30")
    
    # MoCA Score validation
    if data['moca_score'] < 0 or data['moca_score'] > 30:
        errors.append("MoCA Score must be between 0 and 30")
    
    # ADL Score validation
    if data['adl_score'] < 0 or data['adl_score'] > 6:
        errors.append("ADL Score must be between 0 and 6")
    
    # IADL Score validation
    if data['iadl_score'] < 0 or data['iadl_score'] > 8:
        errors.append("IADL Score must be between 0 and 8")
    
    # GDS Score validation
    if data['gds_score'] < 0 or data['gds_score'] > 15:
        errors.append("GDS Score must be between 0 and 15")
    
    # Comorbidities validation
    if data['comorbidities'] < 0:
        errors.append("Number of comorbidities cannot be negative")
    
    return errors

def save_web_prediction(patient_data, prediction_result):
    """Save prediction result from web interface"""
    try:
        # Create a prediction record without patient assessment
        prediction_doc = frappe.get_doc({
            'doctype': 'Care Placement Prediction',
            'patient_name': f"Web User - {now_datetime().strftime('%Y-%m-%d %H:%M')}",
            'prediction_date': prediction_result['prediction_date'],
            'predicted_care_type': prediction_result['prediction'],
            'confidence_score': prediction_result['confidence'] * 100,
            'model_version': prediction_result['model_version'],
            'probabilities': json.dumps(prediction_result['probabilities']),
            'feature_contributions': json.dumps(prediction_result.get('feature_contributions', [])),
            'status': 'Active',
            'source': 'Web Interface',
            # Store input data as JSON for reference
            'input_data': json.dumps(patient_data)
        })
        prediction_doc.insert(ignore_permissions=True)
        frappe.db.commit()
        
        return prediction_doc.name
        
    except Exception as e:
        frappe.logger().error(f"Failed to save web prediction: {str(e)}")
        return None
